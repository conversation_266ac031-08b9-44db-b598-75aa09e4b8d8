import React, { useEffect } from "react";
import { AuthProvider } from "./contexts/AuthContext";
import { PaymentProvider } from "./contexts/PaymentContext";
import Routes from "./Routes";
import OfflineIndicator from "./components/OfflineIndicator";
import swManager from "./services/swManager";
import ErrorBoundary from "./components/ErrorBoundary";

function App() {
  // Initialize PWA features
  useEffect(() => {
    const initializePWA = async () => {
      try {
        // Initialize service worker
        const swReady = await swManager.init();
        
        if (swReady) {
          console.log('PWA services initialized successfully');
          
          // Set up update notifications
          swManager.on('update-available', () => {
            console.log('App update available');
            // You can show a toast notification here
          });
          
          // Set up sync events
          swManager.on('sync-success', (event) => {
            console.log('Background sync completed:', event.detail);
          });
          
          swManager.on('sync-failed', (event) => {
            console.error('Background sync failed:', event.detail);
          });
        }
      } catch (error) {
        console.error('Failed to initialize PWA:', error);
      }
    };

    initializePWA();
  }, []);

  return (
    <ErrorBoundary>
      <AuthProvider>
        <PaymentProvider>
          <div className="App">
            <Routes />
            
            {/* PWA Components */}
            <div className="fixed top-4 right-4 z-50">
              <OfflineIndicator showDetails={false} />
            </div>
            
            {/* Paystack Script */}
            <script 
              src="https://js.paystack.co/v1/inline.js"
              onLoad={() => {
                window.PaystackPop = window.PaystackPop || {};
              }}
            />
          </div>
        </PaymentProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;