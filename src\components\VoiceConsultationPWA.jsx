/**
 * Voice Consultation with PWA Offline Support
 * Example integration of all PWA features for voice consultations
 */

import React, { useState, useEffect } from 'react';
import { useSpeechSocket } from '../hooks/useSpeechSocket';
import usePWA from '../hooks/usePWA';
import OfflineIndicator from './OfflineIndicator';
import { Mic, MicOff, Play, Pause, Wifi, WifiOff, RefreshCw, Database, AlertTriangle } from 'lucide-react';

const VoiceConsultationPWA = ({ sessionId, patientId }) => {
  const [consultationState, setConsultationState] = useState('idle'); // idle, recording, processing, completed
  const [showOfflineWarning, setShowOfflineWarning] = useState(false);
  
  // PWA hooks
  const pwa = usePWA();
  const speech = useSpeechSocket({
    sessionId,
    autoInit: true,
    autoSync: true,
    maxChunkSize: 64 * 1024,
    compressionEnabled: true
  });

  // Monitor offline state
  useEffect(() => {
    if (!pwa.isOnline && consultationState === 'recording') {
      setShowOfflineWarning(true);
    } else {
      setShowOfflineWarning(false);
    }
  }, [pwa.isOnline, consultationState]);

  // Auto-sync when connection is restored
  useEffect(() => {
    if (pwa.isOnline && speech.hasPendingSync) {
      speech.syncPendingChunks();
    }
  }, [pwa.isOnline, speech.hasPendingSync]);

  const handleStartRecording = async () => {
    try {
      setConsultationState('recording');
      await speech.startRecording();
    } catch (error) {
      console.error('Failed to start recording:', error);
      setConsultationState('idle');
    }
  };

  const handleStopRecording = async () => {
    try {
      setConsultationState('processing');
      await speech.stopRecording();
      
      // If offline, show appropriate message
      if (speech.offlineMode) {
        setTimeout(() => setConsultationState('idle'), 1000);
      } else {
        // Wait for processing to complete
        setTimeout(() => setConsultationState('completed'), 2000);
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      setConsultationState('idle');
    }
  };

  const handleForceSync = async () => {
    try {
      await pwa.forceSync();
    } catch (error) {
      console.error('Force sync failed:', error);
    }
  };

  const handleInstallApp = async () => {
    try {
      await pwa.installApp();
    } catch (error) {
      console.error('App installation failed:', error);
    }
  };

  const getConnectionStatusColor = () => {
    if (!pwa.isOnline) return 'text-red-500';
    if (speech.connectionQuality === 'green') return 'text-green-500';
    if (speech.connectionQuality === 'yellow') return 'text-yellow-500';
    return 'text-red-500';
  };

  const getConnectionStatusIcon = () => {
    if (!pwa.isOnline) return <WifiOff className="w-5 h-5" />;
    return <Wifi className="w-5 h-5" />;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header with PWA Status */}
      <div className="flex items-center justify-between bg-white rounded-lg shadow-sm p-4">
        <h2 className="text-xl font-semibold text-gray-900">
          Voice Consultation {speech.offlineMode && '(Offline Mode)'}
        </h2>
        
        <div className="flex items-center space-x-4">
          <OfflineIndicator showDetails={true} />
          
          {pwa.canInstall && (
            <button
              onClick={handleInstallApp}
              className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
            >
              Install App
            </button>
          )}
        </div>
      </div>

      {/* Connection Status */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={getConnectionStatusColor()}>
              {getConnectionStatusIcon()}
            </div>
            <div>
              <p className="font-medium">
                {pwa.isOnline ? 'Online' : 'Offline'}
              </p>
              <p className="text-sm text-gray-500">
                {speech.isConnected ? 'Real-time processing' : 'Voice stored locally'}
              </p>
            </div>
          </div>
          
          {(pwa.hasPendingSync || speech.persistenceStatus?.pendingSyncChunks > 0) && (
            <button
              onClick={handleForceSync}
              disabled={pwa.syncInProgress}
              className="flex items-center space-x-2 px-3 py-2 bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${pwa.syncInProgress ? 'animate-spin' : ''}`} />
              <span>Sync ({speech.persistenceStatus?.pendingSyncChunks || 0})</span>
            </button>
          )}
        </div>
      </div>

      {/* Offline Warning */}
      {showOfflineWarning && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800">Recording in Offline Mode</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Your voice is being recorded and stored locally. It will be processed when connection is restored.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Recording Interface */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center space-y-6">
          {/* Record Button */}
          <div className="flex justify-center">
            {!speech.isRecording ? (
              <button
                onClick={handleStartRecording}
                disabled={consultationState === 'processing'}
                className="w-20 h-20 bg-red-500 hover:bg-red-600 disabled:opacity-50 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-105"
              >
                <Mic className="w-8 h-8 text-white" />
              </button>
            ) : (
              <button
                onClick={handleStopRecording}
                className="w-20 h-20 bg-gray-500 hover:bg-gray-600 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-105"
              >
                <MicOff className="w-8 h-8 text-white" />
              </button>
            )}
          </div>

          {/* Status */}
          <div>
            {consultationState === 'idle' && (
              <p className="text-gray-600">Tap to start recording your symptoms</p>
            )}
            
            {consultationState === 'recording' && (
              <div className="space-y-2">
                <p className="text-red-600 font-medium">Recording...</p>
                <div className="w-32 mx-auto">
                  <div className="flex justify-center space-x-1">
                    {[...Array(4)].map((_, i) => (
                      <div
                        key={i}
                        className="w-2 h-8 bg-red-400 rounded animate-pulse"
                        style={{ animationDelay: `${i * 0.1}s` }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {consultationState === 'processing' && (
              <div className="space-y-2">
                <p className="text-blue-600 font-medium">
                  {speech.offlineMode ? 'Stored for Processing' : 'Processing...'}
                </p>
                <div className="w-8 h-8 mx-auto">
                  <RefreshCw className="w-8 h-8 text-blue-500 animate-spin" />
                </div>
              </div>
            )}
            
            {consultationState === 'completed' && (
              <p className="text-green-600 font-medium">Recording completed</p>
            )}
          </div>
        </div>
      </div>

      {/* Transcription Results */}
      {speech.transcripts.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="font-medium text-gray-900 mb-3">Transcription</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {speech.transcripts.map((transcript, index) => (
              <div key={index} className="p-2 bg-gray-50 rounded text-sm">
                <span className="text-gray-500 text-xs">
                  {new Date(transcript.timestamp).toLocaleTimeString()}
                </span>
                <p className="mt-1">{transcript.text}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Storage Status */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Database className="w-5 h-5 text-gray-500" />
            <div>
              <p className="font-medium text-gray-900">Local Storage</p>
              <p className="text-sm text-gray-500">
                {speech.persistenceStatus?.storedChunks || 0} voice chunks stored
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <p className="text-sm text-gray-500">
              {((speech.persistenceStatus?.totalStorageUsed || 0) / 1024 / 1024).toFixed(1)} MB used
            </p>
            {speech.persistenceStatus?.lastSync && (
              <p className="text-xs text-gray-400">
                Last sync: {new Date(speech.persistenceStatus.lastSync).toLocaleString()}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Debug Panel (Development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">Debug Info</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p><strong>PWA:</strong> {pwa.swReady ? 'Ready' : 'Not Ready'}</p>
              <p><strong>Network:</strong> {pwa.isOnline ? 'Online' : 'Offline'}</p>
              <p><strong>SW State:</strong> {pwa.swState}</p>
            </div>
            <div>
              <p><strong>Speech Engine:</strong> {speech.isConnected ? 'Connected' : 'Disconnected'}</p>
              <p><strong>Quality:</strong> {speech.connectionQuality}</p>
              <p><strong>Offline Mode:</strong> {speech.offlineMode ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceConsultationPWA;
