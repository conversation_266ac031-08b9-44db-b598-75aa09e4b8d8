-- Location: supabase/migrations/20241216120000_auth_and_consultation_system.sql
-- VoiceHealth AI Multi-Agent Consultation System with Authentication

-- 1. Custom Types
CREATE TYPE public.user_role AS ENUM ('patient', 'provider', 'admin');
CREATE TYPE public.agent_specialty AS ENUM ('general_practitioner', 'cardiologist', 'nutritionist', 'psychiatrist', 'dermatologist', 'neurologist');
CREATE TYPE public.consultation_status AS ENUM ('scheduled', 'active', 'paused', 'completed', 'cancelled');
CREATE TYPE public.session_phase AS ENUM ('ready', 'active', 'agent_consultation', 'paused', 'completed');
CREATE TYPE public.audio_quality AS ENUM ('high', 'medium', 'low');

-- 2. User Profiles Table (Critical intermediary for auth relationships)
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    role public.user_role DEFAULT 'patient'::public.user_role,
    avatar_url TEXT,
    phone TEXT,
    date_of_birth DATE,
    gender TEXT,
    preferred_language TEXT DEFAULT 'English',
    profile_completion_percentage INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 3. Medical History Tables
CREATE TABLE public.medical_conditions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    condition_name TEXT NOT NULL,
    diagnosed_date DATE,
    is_current BOOLEAN DEFAULT true,
    severity TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.medications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    medication_name TEXT NOT NULL,
    dosage TEXT,
    frequency TEXT,
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN DEFAULT true,
    prescribed_by TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 4. AI Agents System
CREATE TABLE public.ai_agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    specialty public.agent_specialty NOT NULL,
    avatar_url TEXT,
    language TEXT DEFAULT 'English',
    voice_id TEXT,
    personality_traits JSONB,
    capabilities TEXT[],
    is_active BOOLEAN DEFAULT true,
    confidence_score DECIMAL(3,2) DEFAULT 0.90,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 5. Consultation Sessions
CREATE TABLE public.consultation_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    primary_agent_id UUID REFERENCES public.ai_agents(id),
    session_title TEXT,
    status public.consultation_status DEFAULT 'scheduled'::public.consultation_status,
    current_phase public.session_phase DEFAULT 'ready'::public.session_phase,
    started_at TIMESTAMPTZ,
    ended_at TIMESTAMPTZ,
    duration_seconds INTEGER DEFAULT 0,
    progress_percentage INTEGER DEFAULT 0,
    is_emergency BOOLEAN DEFAULT false,
    session_notes TEXT,
    key_symptoms TEXT[],
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 6. Agent Participation in Sessions
CREATE TABLE public.session_agent_participation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES public.ai_agents(id),
    joined_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMPTZ,
    is_primary BOOLEAN DEFAULT false,
    contribution_summary TEXT,
    confidence_score DECIMAL(3,2),
    UNIQUE(session_id, agent_id)
);

-- 7. Conversation History
CREATE TABLE public.conversation_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    speaker_type TEXT NOT NULL CHECK (speaker_type IN ('user', 'agent')),
    speaker_id UUID, -- user_id or agent_id
    speaker_name TEXT NOT NULL,
    content TEXT NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    confidence_score DECIMAL(3,2),
    is_transcribed BOOLEAN DEFAULT false,
    audio_url TEXT,
    sequence_number INTEGER
);

-- 8. Audio Sessions and Files
CREATE TABLE public.audio_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    audio_file_url TEXT,
    duration_seconds INTEGER,
    quality public.audio_quality DEFAULT 'medium'::public.audio_quality,
    file_size_bytes BIGINT,
    has_transcript BOOLEAN DEFAULT false,
    transcript_url TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 9. Session Recommendations
CREATE TABLE public.session_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    recommending_agent_id UUID REFERENCES public.ai_agents(id),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    priority TEXT CHECK (priority IN ('Low', 'Medium', 'High', 'Critical')),
    category TEXT,
    is_implemented BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 10. Essential Indexes
CREATE INDEX idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX idx_user_profiles_role ON public.user_profiles(role);
CREATE INDEX idx_medical_conditions_user_id ON public.medical_conditions(user_id);
CREATE INDEX idx_medications_user_id ON public.medications(user_id);
CREATE INDEX idx_consultation_sessions_patient_id ON public.consultation_sessions(patient_id);
CREATE INDEX idx_consultation_sessions_status ON public.consultation_sessions(status);
CREATE INDEX idx_conversation_messages_session_id ON public.conversation_messages(session_id);
CREATE INDEX idx_conversation_messages_timestamp ON public.conversation_messages(timestamp);
CREATE INDEX idx_audio_sessions_session_id ON public.audio_sessions(session_id);
CREATE INDEX idx_session_recommendations_session_id ON public.session_recommendations(session_id);

-- 11. Enable Row Level Security
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medical_conditions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.consultation_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.session_agent_participation ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audio_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.session_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_agents ENABLE ROW LEVEL SECURITY;

-- 12. Helper Functions for RLS
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_profiles up
    WHERE up.id = auth.uid() AND up.role = 'admin'::public.user_role
)
$$;

CREATE OR REPLACE FUNCTION public.is_provider()
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_profiles up
    WHERE up.id = auth.uid() AND up.role = 'provider'::public.user_role
)
$$;

CREATE OR REPLACE FUNCTION public.can_access_session(session_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.consultation_sessions cs
    WHERE cs.id = session_uuid AND (
        cs.patient_id = auth.uid() OR
        public.is_admin() OR
        public.is_provider()
    )
)
$$;

-- 13. RLS Policies
-- User Profiles
CREATE POLICY "users_own_profile" ON public.user_profiles FOR ALL
USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

CREATE POLICY "admins_view_all_profiles" ON public.user_profiles FOR SELECT
USING (public.is_admin());

-- Medical Conditions & Medications
CREATE POLICY "users_own_medical_data" ON public.medical_conditions FOR ALL
USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "providers_view_medical_data" ON public.medical_conditions FOR SELECT
USING (public.is_provider() OR public.is_admin());

CREATE POLICY "users_own_medications" ON public.medications FOR ALL
USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "providers_view_medications" ON public.medications FOR SELECT
USING (public.is_provider() OR public.is_admin());

-- AI Agents (Public read access)
CREATE POLICY "public_read_agents" ON public.ai_agents FOR SELECT
TO authenticated USING (true);

CREATE POLICY "admins_manage_agents" ON public.ai_agents FOR ALL
USING (public.is_admin()) WITH CHECK (public.is_admin());

-- Consultation Sessions
CREATE POLICY "session_access_control" ON public.consultation_sessions FOR ALL
USING (public.can_access_session(id)) WITH CHECK (public.can_access_session(id));

-- Session-related tables (inherit session access)
CREATE POLICY "agent_participation_access" ON public.session_agent_participation FOR ALL
USING (public.can_access_session(session_id)) WITH CHECK (public.can_access_session(session_id));

CREATE POLICY "conversation_access" ON public.conversation_messages FOR ALL
USING (public.can_access_session(session_id)) WITH CHECK (public.can_access_session(session_id));

CREATE POLICY "audio_session_access" ON public.audio_sessions FOR ALL
USING (public.can_access_session(session_id)) WITH CHECK (public.can_access_session(session_id));

CREATE POLICY "recommendations_access" ON public.session_recommendations FOR ALL
USING (public.can_access_session(session_id)) WITH CHECK (public.can_access_session(session_id));

-- 14. Functions for automatic profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, full_name, role)
  VALUES (
    NEW.id, 
    NEW.email, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'role', 'patient')::public.user_role
  );  
  RETURN NEW;
END;
$$;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 15. Default AI Agents Data
DO $$
DECLARE
    gp_agent_id UUID := gen_random_uuid();
    cardio_agent_id UUID := gen_random_uuid();
    nutri_agent_id UUID := gen_random_uuid();
BEGIN
    INSERT INTO public.ai_agents (id, name, specialty, avatar_url, language, personality_traits, capabilities, confidence_score)
    VALUES
        (gp_agent_id, 'Dr. Sarah Chen', 'general_practitioner'::public.agent_specialty, 
         'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400&h=400&fit=crop&crop=face',
         'English', 
         '{"empathetic": true, "detail_oriented": true, "calm": true}'::jsonb,
         ARRAY['Primary Care', 'Diagnosis', 'Treatment Planning', 'Patient Communication'],
         0.95),
        (cardio_agent_id, 'Dr. Michael Rodriguez', 'cardiologist'::public.agent_specialty,
         'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
         'English',
         '{"analytical": true, "precise": true, "thorough": true}'::jsonb,
         ARRAY['Heart Health', 'Cardiovascular Assessment', 'Risk Analysis', 'Emergency Response'],
         0.92),
        (nutri_agent_id, 'Dr. Emily Watson', 'nutritionist'::public.agent_specialty,
         'https://images.unsplash.com/photo-1594824388853-e4d2b7b3e3e3?w=400&h=400&fit=crop&crop=face',
         'English',
         '{"encouraging": true, "practical": true, "holistic": true}'::jsonb,
         ARRAY['Nutrition Planning', 'Dietary Assessment', 'Lifestyle Coaching', 'Weight Management'],
         0.88);
END $$;

-- 16. Sample Data for Testing
DO $$
DECLARE
    demo_patient_id UUID := gen_random_uuid();
    demo_provider_id UUID := gen_random_uuid();
    demo_admin_id UUID := gen_random_uuid();
    sample_session_id UUID := gen_random_uuid();
    gp_agent_id UUID;
BEGIN
    -- Get GP agent ID
    SELECT id INTO gp_agent_id FROM public.ai_agents WHERE specialty = 'general_practitioner'::public.agent_specialty LIMIT 1;

    -- Create demo auth users with required fields
    INSERT INTO auth.users (
        id, instance_id, aud, role, email, encrypted_password, email_confirmed_at,
        created_at, updated_at, raw_user_meta_data, raw_app_meta_data,
        is_sso_user, is_anonymous, confirmation_token, confirmation_sent_at,
        recovery_token, recovery_sent_at, email_change_token_new, email_change,
        email_change_sent_at, email_change_token_current, email_change_confirm_status,
        reauthentication_token, reauthentication_sent_at, phone, phone_change,
        phone_change_token, phone_change_sent_at
    ) VALUES
        (demo_patient_id, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('HealthDemo123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Demo Patient", "role": "patient"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (demo_provider_id, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('ProviderDemo123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Dr. Demo Provider", "role": "provider"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (demo_admin_id, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('AdminDemo123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Demo Admin", "role": "admin"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null);

    -- Update user profiles with additional data
    UPDATE public.user_profiles 
    SET 
        avatar_url = 'https://api.dicebear.com/7.x/avataaars/svg?seed=patient',
        profile_completion_percentage = 75,
        preferred_language = 'English'
    WHERE id = demo_patient_id;

    -- Sample medical history
    INSERT INTO public.medical_conditions (user_id, condition_name, diagnosed_date, is_current, severity)
    VALUES
        (demo_patient_id, 'Hypertension', '2022-03-15', true, 'Moderate'),
        (demo_patient_id, 'Type 2 Diabetes', '2021-08-20', true, 'Well Controlled');

    INSERT INTO public.medications (user_id, medication_name, dosage, frequency, start_date, is_current)
    VALUES
        (demo_patient_id, 'Lisinopril', '10mg', 'Once daily', '2022-03-15', true),
        (demo_patient_id, 'Metformin', '500mg', 'Twice daily', '2021-08-20', true);

    -- Sample consultation session
    INSERT INTO public.consultation_sessions (id, patient_id, primary_agent_id, session_title, status, duration_seconds, progress_percentage)
    VALUES
        (sample_session_id, demo_patient_id, gp_agent_id, 'General Health Consultation', 'completed'::public.consultation_status, 1800, 100);

    -- Sample conversation
    INSERT INTO public.conversation_messages (session_id, speaker_type, speaker_id, speaker_name, content, confidence_score, sequence_number)
    VALUES
        (sample_session_id, 'user', demo_patient_id, 'Demo Patient', 
         'Hello, I have been experiencing some chest discomfort lately, especially after physical activity.', 
         0.96, 1),
        (sample_session_id, 'agent', gp_agent_id, 'Dr. Sarah Chen',
         'Thank you for sharing that with me. Chest discomfort after physical activity is something we need to take seriously. Can you describe the type of discomfort?',
         0.98, 2);

    -- Sample recommendations
    INSERT INTO public.session_recommendations (session_id, recommending_agent_id, title, content, priority, category)
    VALUES
        (sample_session_id, gp_agent_id, 'Cardiac Evaluation', 
         'Schedule ECG and stress test within 48 hours due to exercise-induced chest pressure', 
         'High', 'Diagnostic Testing');

END $$;