/**
 * Text-to-Speech Service using ElevenLabs API
 */
class TextToSpeechService {
  constructor() {
    this.apiKey = import.meta.env.VITE_ELEVENLABS_API_KEY;
    this.baseUrl = 'https://api.elevenlabs.io/v1';
    this.defaultVoiceId = 'ErXwobaYiN019PkySvjV'; // <PERSON><PERSON> voice (clear, professional)
    this.fallbackVoiceId = 'EXAVITQu4vr4xnSDxMaL'; // Sarah voice (warm, caring)
    
    // Voice configurations for different agent types
    this.agentVoices = {
      'general-practitioner': 'ErXwobaYiN019PkySvjV', // Antoni - professional
      'cardiologist': 'VR6AewLTigWG4xSOukaG', // Arnold - authoritative
      'pediatrician': 'EXAVITQu4vr4xnSDxMaL', // Sarah - warm
      'mental-health': 'pNInz6obpgDQGcFmaJgB', // Adam - calm
      'default': 'ErXwobaYiN019PkySvjV'
    };
  }

  /**
   * Convert text to speech using ElevenLabs API
   */
  async synthesizeSpeech(text, options = {}) {
    try {
      console.log('🔊 Starting text-to-speech synthesis...', { 
        text: text.substring(0, 50) + '...', 
        voice: options.voiceId || 'default' 
      });

      const voiceId = this.getVoiceForAgent(options.agentType) || options.voiceId || this.defaultVoiceId;
      
      const response = await fetch(`${this.baseUrl}/text-to-speech/${voiceId}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': this.apiKey
        },
        body: JSON.stringify({
          text,
          model_id: options.model || 'eleven_monolingual_v1',
          voice_settings: {
            stability: options.stability || 0.5,
            similarity_boost: options.similarity_boost || 0.8,
            style: options.style || 0.0,
            use_speaker_boost: true
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ ElevenLabs API error:', response.status, errorText);
        throw new Error(`ElevenLabs API error: ${response.status} - ${errorText}`);
      }

      const audioBlob = await response.blob();
      console.log('✅ Speech synthesis successful:', { size: audioBlob.size });

      return {
        audioBlob,
        audioUrl: URL.createObjectURL(audioBlob),
        duration: this.estimateDuration(text),
        voiceId
      };

    } catch (error) {
      console.error('❌ Text-to-speech error:', error);
      
      // Fallback to mock speech for development
      if (error.message.includes('API key') || error.message.includes('401')) {
        console.warn('🔄 Falling back to mock speech - API key not configured');
        return this.getMockSpeech(text);
      }
      
      throw error;
    }
  }

  /**
   * Get appropriate voice for agent type
   */
  getVoiceForAgent(agentType) {
    return this.agentVoices[agentType] || this.agentVoices.default;
  }

  /**
   * Estimate speech duration based on text length
   */
  estimateDuration(text) {
    // Average speaking rate: ~150 words per minute
    const words = text.split(' ').length;
    const minutes = words / 150;
    return Math.max(1, Math.round(minutes * 60 * 1000)); // Convert to milliseconds
  }

  /**
   * Mock speech synthesis for development/testing
   */
  getMockSpeech(text) {
    const duration = this.estimateDuration(text);
    
    // Create a simple audio context for silence
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const buffer = audioContext.createBuffer(1, audioContext.sampleRate * (duration / 1000), audioContext.sampleRate);
    
    // Create a simple tone instead of silence for better testing
    const channelData = buffer.getChannelData(0);
    for (let i = 0; i < channelData.length; i++) {
      channelData[i] = Math.sin(2 * Math.PI * 440 * i / audioContext.sampleRate) * 0.1;
    }

    return new Promise((resolve) => {
      const offlineContext = new OfflineAudioContext(1, buffer.length, audioContext.sampleRate);
      const source = offlineContext.createBufferSource();
      source.buffer = buffer;
      source.connect(offlineContext.destination);
      source.start(0);
      
      offlineContext.startRendering().then((renderedBuffer) => {
        // Convert to wav blob
        const audioBlob = this.bufferToWav(renderedBuffer);
        
        resolve({
          audioBlob,
          audioUrl: URL.createObjectURL(audioBlob),
          duration,
          voiceId: 'mock-voice',
          isMock: true
        });
      });
    });
  }

  /**
   * Convert AudioBuffer to WAV blob
   */
  bufferToWav(buffer) {
    const length = buffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    
    // WAV header
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, buffer.sampleRate, true);
    view.setUint32(28, buffer.sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    // Audio data
    const channelData = buffer.getChannelData(0);
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }

  /**
   * Play synthesized audio
   */
  async playAudio(audioUrl, options = {}) {
    return new Promise((resolve, reject) => {
      const audio = new Audio(audioUrl);
      
      audio.onloadedmetadata = () => {
        console.log('🎵 Audio loaded, duration:', audio.duration);
      };
      
      audio.onended = () => {
        console.log('✅ Audio playback completed');
        URL.revokeObjectURL(audioUrl); // Clean up
        resolve();
      };
      
      audio.onerror = (error) => {
        console.error('❌ Audio playback error:', error);
        reject(error);
      };
      
      audio.volume = options.volume || 0.8;
      audio.playbackRate = options.speed || 1.0;
      
      audio.play().catch(reject);
    });
  }

  /**
   * Check if service is configured properly
   */
  isConfigured() {
    return Boolean(this.apiKey);
  }

  /**
   * Health check for the service
   */
  async healthCheck() {
    try {
      if (!this.apiKey) {
        return { status: 'error', message: 'API key not configured' };
      }
      
      // Test with a simple voices endpoint
      const response = await fetch(`${this.baseUrl}/voices`, {
        headers: {
          'xi-api-key': this.apiKey
        }
      });
      
      if (response.ok) {
        return { status: 'healthy', message: 'Service configured and ready' };
      } else {
        return { status: 'error', message: `API returned ${response.status}` };
      }
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }
}

export default new TextToSpeechService();
