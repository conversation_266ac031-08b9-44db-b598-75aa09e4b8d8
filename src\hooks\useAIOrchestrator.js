import { useState, useEffect, useCallback } from 'react';
import aiOrchestrator from '../services/aiOrchestrator';

/**
 * Hook for managing AI Orchestrator interactions
 */
export function useAIOrchestrator() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [providers, setProviders] = useState([]);
  const [healthStatus, setHealthStatus] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Initialize orchestrator on mount
  useEffect(() => {
    const initializeOrchestrator = async () => {
      try {
        setIsLoading(true);
        await aiOrchestrator.initialize();
        setProviders(aiOrchestrator.getAvailableProviders());
        setIsInitialized(true);
        console.log('✅ AI Orchestrator initialized successfully');
      } catch (err) {
        console.error('❌ Failed to initialize AI Orchestrator:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    initializeOrchestrator();
  }, []);

  // Check provider health periodically
  useEffect(() => {
    if (!isInitialized) return;

    const checkHealth = async () => {
      try {
        const status = await aiOrchestrator.checkProvidersHealth();
        setHealthStatus(status);
      } catch (err) {
        console.warn('⚠️ Health check failed:', err);
      }
    };

    // Initial health check
    checkHealth();

    // Set up periodic health checks every 5 minutes
    const healthInterval = setInterval(checkHealth, 5 * 60 * 1000);

    return () => clearInterval(healthInterval);
  }, [isInitialized]);

  // Generate AI response
  const generateResponse = useCallback(async (request) => {
    if (!isInitialized) {
      throw new Error('AI Orchestrator not initialized');
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🤖 Generating response with AI Orchestrator...', {
        agentType: request.agentType,
        complexity: request.complexity,
        messageCount: request.messages?.length
      });

      const response = await aiOrchestrator.generateResponse(request);
      
      console.log('✅ AI response generated successfully', {
        provider: response.provider,
        confidence: response.confidence,
        tokenUsage: response.usage
      });

      return response;
    } catch (err) {
      console.error('❌ AI response generation failed:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized]);

  // Select best provider for request
  const selectProvider = useCallback((complexity, options = {}) => {
    if (!isInitialized) {
      throw new Error('AI Orchestrator not initialized');
    }

    return aiOrchestrator.selectProvider(complexity, options);
  }, [isInitialized]);

  // Get provider statistics
  const getProviderStats = useCallback(() => {
    if (!isInitialized) return null;
    return aiOrchestrator.getProviderStats();
  }, [isInitialized]);

  // Reset error state
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Get available medical agents
  const getAvailableAgents = useCallback(() => {
    return [
      {
        id: 'general-practitioner',
        name: 'General Practitioner',
        specialization: 'General Medicine',
        description: 'Primary care physician for general health consultations'
      },
      {
        id: 'cardiologist',
        name: 'Cardiologist',
        specialization: 'Cardiology',
        description: 'Heart and cardiovascular system specialist'
      },
      {
        id: 'pediatrician',
        name: 'Pediatrician',
        specialization: 'Pediatrics',
        description: 'Children\'s health specialist'
      },
      {
        id: 'mental-health',
        name: 'Mental Health Counselor',
        specialization: 'Mental Health',
        description: 'Mental health and wellness specialist'
      },
      {
        id: 'dermatologist',
        name: 'Dermatologist',
        specialization: 'Dermatology',
        description: 'Skin, hair, and nail specialist'
      }
    ];
  }, []);

  return {
    // State
    isInitialized,
    isLoading,
    error,
    providers,
    healthStatus,
    
    // Actions
    generateResponse,
    selectProvider,
    getProviderStats,
    getAvailableAgents,
    clearError,
    
    // Computed
    isHealthy: isInitialized && Object.values(healthStatus).some(status => status === 'healthy'),
    availableProviders: providers.filter(p => healthStatus[p] === 'healthy')
  };
}
