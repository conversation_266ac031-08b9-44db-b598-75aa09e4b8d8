# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Paystack Configuration
VITE_PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
# SECURITY: Secret key moved to secure backend API
# VITE_PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key  # REMOVED FOR SECURITY

# AI Service Configuration
VITE_OPENAI_API_KEY=sk-your_openai_api_key
VITE_ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key
VITE_COHERE_API_KEY=your_cohere_api_key

# Voice Services
VITE_ELEVENLABS_API_KEY=your_elevenlabs_api_key
VITE_SPEECH_WEBSOCKET_URL=wss://api.elevenlabs.io/v1/text-to-speech/stream

# Emergency & Monitoring
VITE_EMERGENCY_WEBHOOK_URL=https://your-emergency-webhook-url.com/webhook
VITE_ERROR_REPORTING_URL=https://your-monitoring-service.com/errors

# Optional: Development/Production flags
VITE_NODE_ENV=development
VITE_API_BASE_URL=https://your-secure-backend-api.com/api

# Security Configuration
VITE_ENCRYPTION_ENABLED=true
VITE_AUDIT_LOGGING_ENABLED=true