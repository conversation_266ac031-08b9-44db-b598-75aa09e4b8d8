import React, { useState, useEffect } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

const ProfileCompletionPrompt = ({ 
  profileCompletion = 0,
  missingFields = [],
  onCompleteProfile = () => {},
  onDismiss = () => {},
  isVisible = true,
  className = ''
}) => {
  const [isDismissed, setIsDismissed] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // Auto-dismiss if profile is complete
    if (profileCompletion >= 100) {
      setIsDismissed(true);
    }
  }, [profileCompletion]);

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss();
  };

  const handleCompleteProfile = () => {
    onCompleteProfile();
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const getCompletionColor = () => {
    if (profileCompletion >= 80) return 'success';
    if (profileCompletion >= 50) return 'warning';
    return 'error';
  };

  const getCompletionMessage = () => {
    if (profileCompletion >= 80) {
      return 'Almost ready for consultations';
    }
    if (profileCompletion >= 50) {
      return 'Profile partially complete';
    }
    return 'Complete your profile for better consultations';
  };

  const getPriorityFields = () => {
    const priorityOrder = [
      'medical_history',
      'current_medications',
      'allergies',
      'emergency_contact',
      'insurance_info',
      'preferences'
    ];
    
    return missingFields
      .sort((a, b) => priorityOrder.indexOf(a.key) - priorityOrder.indexOf(b.key))
      .slice(0, 3);
  };

  if (!isVisible || isDismissed || profileCompletion >= 100) {
    return null;
  }

  return (
    <div className={`fixed top-20 right-4 z-200 max-w-sm ${className}`}>
      <div className="bg-surface border border-border rounded-xl shadow-elevated overflow-hidden">
        {/* Header */}
        <div className="p-4 pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                getCompletionColor() === 'success' ? 'bg-success-50' :
                getCompletionColor() === 'warning'? 'bg-warning-50' : 'bg-error-50'
              }`}>
                <Icon 
                  name={
                    getCompletionColor() === 'success' ? 'CheckCircle' :
                    getCompletionColor() === 'warning'? 'AlertTriangle' : 'AlertCircle'
                  }
                  size={20} 
                  color={`var(--color-${getCompletionColor()})`}
                />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-text-primary font-heading text-sm">
                  Profile Setup
                </h3>
                <p className="text-xs text-text-secondary font-caption">
                  {getCompletionMessage()}
                </p>
              </div>
            </div>
            
            <button
              onClick={handleDismiss}
              className="p-1 hover:bg-secondary-50 rounded-lg transition-fast"
              title="Dismiss"
            >
              <Icon name="X" size={16} color="var(--color-text-secondary)" />
            </button>
          </div>

          {/* Progress Bar */}
          <div className="mt-3">
            <div className="flex justify-between text-xs text-text-secondary mb-1">
              <span>Completion</span>
              <span>{Math.round(profileCompletion)}%</span>
            </div>
            <div className="w-full bg-secondary-100 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  getCompletionColor() === 'success' ? 'bg-success-500' :
                  getCompletionColor() === 'warning'? 'bg-warning-500' : 'bg-error-500'
                }`}
                style={{ width: `${profileCompletion}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Expandable Content */}
        <div className={`transition-all duration-300 ${isExpanded ? 'max-h-96' : 'max-h-0'} overflow-hidden`}>
          <div className="px-4 pb-2">
            {/* Missing Fields */}
            {missingFields.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-text-primary">Missing Information</h4>
                <div className="space-y-1">
                  {getPriorityFields().map((field, index) => (
                    <div 
                      key={field.key || index}
                      className="flex items-center justify-between p-2 bg-secondary-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-2">
                        <Icon 
                          name={
                            field.key === 'medical_history' ? 'FileText' :
                            field.key === 'current_medications' ? 'Pill' :
                            field.key === 'allergies' ? 'AlertTriangle' :
                            field.key === 'emergency_contact' ? 'Phone' :
                            field.key === 'insurance_info'? 'CreditCard' : 'Settings'
                          }
                          size={14} 
                          color="var(--color-text-secondary)"
                        />
                        <span className="text-sm text-text-primary">
                          {field.label || field.key}
                        </span>
                      </div>
                      <span className="text-xs text-error-500 font-medium">
                        Required
                      </span>
                    </div>
                  ))}
                  
                  {missingFields.length > 3 && (
                    <div className="text-center py-1">
                      <span className="text-xs text-text-secondary">
                        +{missingFields.length - 3} more fields
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Benefits */}
            <div className="mt-4 p-3 bg-primary-50 rounded-lg">
              <h4 className="text-sm font-medium text-primary-600 mb-2">
                Complete Profile Benefits
              </h4>
              <ul className="space-y-1 text-xs text-primary-600">
                <li className="flex items-center space-x-2">
                  <Icon name="Check" size={12} />
                  <span>Personalized health recommendations</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Icon name="Check" size={12} />
                  <span>Faster consultation setup</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Icon name="Check" size={12} />
                  <span>Better agent matching</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="p-4 pt-2 border-t border-border bg-secondary-50">
          <div className="flex space-x-2">
            <Button
              variant="primary"
              size="sm"
              onClick={handleCompleteProfile}
              iconName="ArrowRight"
              iconPosition="right"
              className="flex-1"
            >
              Complete Profile
            </Button>
            
            <button
              onClick={toggleExpanded}
              className="px-3 py-2 bg-surface hover:bg-secondary-100 border border-border rounded-lg transition-fast"
              title={isExpanded ? "Show less" : "Show details"}
            >
              <Icon 
                name={isExpanded ? "ChevronUp" : "ChevronDown"} 
                size={16} 
                color="var(--color-text-secondary)"
              />
            </button>
          </div>
          
          {profileCompletion < 50 && (
            <div className="mt-2 text-center">
              <span className="text-xs text-text-secondary">
                Complete at least 50% to start consultations
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileCompletionPrompt;