import React, { createContext, useContext, useEffect, useState, useCallback } from "react";
import paystackService from "../utils/paystackService";
import { useAuth } from "./AuthContext";
import databaseSetup from '../utils/databaseSetup';
import { supabase } from '../utils/supabaseClient';

const PaymentContext = createContext();

export function PaymentProvider({ children, setError: externalSetError }) {
  const { user, userProfile } = useAuth();
  const [subscription, setSubscription] = useState(null);
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [databaseError, setDatabaseError] = useState(null);
  const [isDatabaseSetupRequired, setIsDatabaseSetupRequired] = useState(false);
  const [internalError, setInternalError] = useState(null);
  
  // Use external setError if provided, otherwise use internal state
  const setError = externalSetError || setInternalError;

  // Load user's payment data when authenticated
  useEffect(() => {
    if (user?.id) {
      loadPaymentData();
    } else {
      // Clear data when user logs out
      setSubscription(null);
      setPaymentHistory([]);
      setLoading(false);
    }
  }, [user?.id]);

  // Load subscription plans on mount
  useEffect(() => {
    loadSubscriptionPlans();
  }, []);

  const loadPaymentData = async () => {
    try {
      setLoading(true);
      setPaymentError(null);

      // Load active subscription
      const subscriptionResult = await paystackService.getUserActiveSubscription(user.id);
      if (subscriptionResult.success) {
        setSubscription(subscriptionResult.data);
      } else {
        console.error('Failed to load subscription:', subscriptionResult.error);
      }

      // Load payment history
      const historyResult = await paystackService.getUserTransactions(user.id);
      if (historyResult.success) {
        setPaymentHistory(historyResult.data || []);
      } else {
        console.error('Failed to load payment history:', historyResult.error);
      }
    } catch (error) {
      console.error('Error loading payment data:', error);
      setPaymentError('Failed to load payment information');
    } finally {
      setLoading(false);
    }
  };

  const loadSubscriptionPlans = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      setDatabaseError(null);

      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_ngn', { ascending: true });

      if (error) {
        // Try to handle database-specific errors
        const wasHandled = await handleDatabaseError(error);

        if (!wasHandled) {
          throw error;
        }

        // Use demo plans as fallback
        setSubscriptionPlans(getDemoPlans());
        return;
      }

      if (!data || data.length === 0) {
        console.warn('No subscription plans found in database, checking setup...');

        // Diagnose the issue
        const diagnosis = await databaseSetup.diagnoseSubscriptionPlans();

        if (!diagnosis.tableExists) {
          setIsDatabaseSetupRequired(true);
          setDatabaseError({
            type: 'table_missing',
            message: 'Subscription plans table not found',
            autoFixAvailable: true,
            diagnosis
          });
        } else if (!diagnosis.hasData) {
          setIsDatabaseSetupRequired(true);
          setDatabaseError({
            type: 'no_data',
            message: 'Subscription plans table is empty',
            autoFixAvailable: true,
            diagnosis
          });
        }

        // Use demo plans as fallback
        setSubscriptionPlans(getDemoPlans());
        return;
      }

      setSubscriptionPlans(data);
      setIsDatabaseSetupRequired(false);
      setDatabaseError(null);
    } catch (error) {
      console.error('Error loading subscription plans:', error);

      // Final fallback to demo plans
      setSubscriptionPlans(getDemoPlans());
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // Enhanced error handling for database issues
  const handleDatabaseError = async (error) => {
    console.error('Database error in PaymentContext:', error);

    if (error?.code === '42P01' || error?.message?.includes('does not exist')) {
      setDatabaseError({
        type: 'missing_table',
        message: 'Database setup required - subscription tables not found',
        autoFixAvailable: true,
        error
      });
      setIsDatabaseSetupRequired(true);

      // Try automatic diagnosis
      try {
        const diagnosis = await databaseSetup.diagnoseSubscriptionPlans();
        setDatabaseError((prev) => ({
          ...prev,
          diagnosis
        }));
      } catch (diagnosisError) {
        console.warn('Could not diagnose database issue:', diagnosisError);
      }

      return true; // Indicates we handled the error
    }

    return false; // Let other error handlers deal with it
  };

  // Auto-fix database setup
  const autoFixDatabase = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔧 Running automatic database setup...');
      const result = await databaseSetup.autoFixDatabaseSetup();

      if (result.success) {
        console.log('✅ Database setup completed successfully');
        setIsDatabaseSetupRequired(false);
        setDatabaseError(null);

        // Reload all data
        await loadPaymentData();

        return { success: true, message: 'Database setup completed successfully' };
      } else {
        console.error('❌ Database setup failed:', result.error);
        setError(`Database setup failed: ${result.error}`);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Auto-fix failed:', error);
      setError(`Auto-fix failed: ${error.message}`);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Get demo plans as fallback
  const getDemoPlans = () => [
  {
    id: 'demo-basic',
    name: 'Basic Plan',
    description: 'Perfect for occasional consultations',
    price_ngn: 5000.00,
    price_usd: 12.00,
    duration_days: 30,
    consultation_credits: 3,
    features: ['3 consultations per month', 'Basic AI agents', 'Text transcripts', 'Email support'],
    is_popular: false,
    is_demo: true
  },
  {
    id: 'demo-premium',
    name: 'Premium Plan',
    description: 'Most popular for regular health monitoring',
    price_ngn: 12000.00,
    price_usd: 28.00,
    duration_days: 30,
    consultation_credits: 10,
    features: ['10 consultations per month', 'All AI specialists', 'Audio recordings', 'Priority support', 'Health insights'],
    is_popular: true,
    is_demo: true
  },
  {
    id: 'demo-professional',
    name: 'Professional Plan',
    description: 'Unlimited access for comprehensive care',
    price_ngn: 25000.00,
    price_usd: 60.00,
    duration_days: 30,
    consultation_credits: 0,
    features: ['Unlimited consultations', 'All premium features', 'Personal health dashboard', '24/7 priority support', 'Advanced analytics'],
    is_popular: false,
    is_demo: true
  }];


  // Initialize payment for subscription
  const subscribeUser = async (planId, currency = 'NGN') => {
    try {
      setIsProcessingPayment(true);
      setPaymentError(null);

      if (!user?.id || !userProfile?.email) {
        throw new Error('User not authenticated');
      }

      // Find the selected plan
      const selectedPlan = subscriptionPlans.find((plan) => plan.id === planId);
      if (!selectedPlan) {
        throw new Error('Selected plan not found');
      }

      // Create subscription record
      const subscriptionResult = await paystackService.createSubscription(user.id, planId);
      if (!subscriptionResult.success) {
        throw new Error(subscriptionResult.error);
      }

      const newSubscription = subscriptionResult.data;

      // Prepare payment data
      const amount = currency === 'USD' ? selectedPlan.price_usd : selectedPlan.price_ngn;
      const reference = paystackService.generateReference('sub');

      const paymentData = {
        email: userProfile.email,
        amount: amount,
        currency: currency,
        reference: reference,
        user_id: user.id,
        subscription_id: newSubscription.id,
        callback_url: `${window.location.origin}/payment-success?reference=${reference}`,
        metadata: {
          plan_name: selectedPlan.name,
          plan_id: planId,
          user_name: userProfile.full_name || userProfile.email
        }
      };

      // Initialize payment
      const paymentResult = await paystackService.initializePayment(paymentData);

      if (!paymentResult.success) {
        throw new Error(paymentResult.error);
      }

      return {
        success: true,
        data: {
          authorization_url: paymentResult.data.authorization_url,
          reference: reference,
          subscription_id: newSubscription.id
        }
      };
    } catch (error) {
      console.error('Subscription error:', error);
      setPaymentError(error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // Pay for single consultation
  const payForConsultation = async (consultationId, amount, currency = 'NGN') => {
    try {
      setIsProcessingPayment(true);
      setPaymentError(null);

      if (!user?.id || !userProfile?.email) {
        throw new Error('User not authenticated');
      }

      const reference = paystackService.generateReference('cons');

      const paymentData = {
        email: userProfile.email,
        amount: amount,
        currency: currency,
        reference: reference,
        user_id: user.id,
        consultation_session_id: consultationId,
        callback_url: `${window.location.origin}/payment-success?reference=${reference}`,
        metadata: {
          consultation_id: consultationId,
          user_name: userProfile.full_name || userProfile.email,
          payment_type: 'consultation'
        }
      };

      const paymentResult = await paystackService.initializePayment(paymentData);

      if (!paymentResult.success) {
        throw new Error(paymentResult.error);
      }

      return {
        success: true,
        data: {
          authorization_url: paymentResult.data.authorization_url,
          reference: reference
        }
      };
    } catch (error) {
      console.error('Consultation payment error:', error);
      setPaymentError(error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // Verify payment and update local state
  const verifyPayment = async (reference) => {
    try {
      setIsProcessingPayment(true);
      setPaymentError(null);

      const result = await paystackService.verifyPayment(reference);

      if (!result.success) {
        throw new Error(result.error);
      }

      // Reload payment data to get updated subscription/history
      await loadPaymentData();

      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      console.error('Payment verification error:', error);
      setPaymentError(error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // Check if user can start consultation
  const checkConsultationEligibility = async () => {
    try {
      if (!user?.id) {
        return {
          success: false,
          error: 'User not authenticated'
        };
      }

      return await paystackService.canStartConsultation(user.id);
    } catch (error) {
      console.error('Eligibility check error:', error);
      return {
        success: false,
        error: 'Failed to check consultation eligibility'
      };
    }
  };

  // Open Paystack popup for payment
  const openPaymentPopup = (paymentData) => {
    return paystackService.openPaystackPopup({
      ...paymentData,
      onClose: () => {
        setIsProcessingPayment(false);
        paymentData.onClose?.();
      },
      callback: (response) => {
        verifyPayment(response.reference).then((result) => {
          paymentData.callback?.(response, result);
        });
      }
    });
  };

  // Refresh subscription data
  const refreshSubscription = async () => {
    if (user?.id) {
      const result = await paystackService.getUserActiveSubscription(user.id);
      if (result.success) {
        setSubscription(result.data);
      }
    }
  };

  // Check if user has active subscription
  const hasActiveSubscription = () => {
    return subscription &&
    subscription.status === 'active' &&
    new Date(subscription.expires_at) > new Date();
  };

  // Get remaining consultation credits
  const getRemainingCredits = () => {
    if (!subscription) return 0;
    if (subscription.subscription_plans?.consultation_credits === 0) return Infinity; // Unlimited
    return subscription.consultation_credits_remaining || 0;
  };

  const value = {
    // State
    subscription,
    subscriptionPlans,
    paymentHistory,
    isProcessingPayment,
    paymentError,
    loading,

    // Actions
    subscribeUser,
    payForConsultation,
    verifyPayment,
    checkConsultationEligibility,
    openPaymentPopup,
    refreshSubscription,
    loadPaymentData,

    // Helpers
    hasActiveSubscription,
    getRemainingCredits,
    clearError: () => setPaymentError(null),
    databaseError,
    isDatabaseSetupRequired,
    autoFixDatabase,
    getDemoPlans
  };

  return (
    <PaymentContext.Provider value={value}>
      {children}
    </PaymentContext.Provider>);

}

export const usePayment = () => {
  const context = useContext(PaymentContext);
  if (!context) {
    throw new Error("usePayment must be used within a PaymentProvider");
  }
  return context;
};

export default PaymentContext;