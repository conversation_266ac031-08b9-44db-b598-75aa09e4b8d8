/**
 * Health Check & Monitoring Service
 * Provides health endpoints for all microservices
 */

class HealthCheckService {
  constructor() {
    this.services = new Map();
    this.checks = [];
    this.startTime = Date.now();
  }

  /**
   * Register a service for health monitoring
   */
  registerService(name, healthCheckFn, options = {}) {
    this.services.set(name, {
      name,
      healthCheck: healthCheckFn,
      timeout: options.timeout || 5000,
      critical: options.critical !== false,
      lastCheck: null,
      status: 'unknown'
    });
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck() {
    const startTime = Date.now();
    const results = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      services: {},
      summary: {
        total: 0,
        healthy: 0,
        unhealthy: 0,
        critical_failed: 0
      }
    };

    const checks = Array.from(this.services.entries()).map(async ([name, service]) => {
      try {
        const checkStart = Date.now();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Health check timeout')), service.timeout)
        );

        const healthResult = await Promise.race([
          service.healthCheck(),
          timeoutPromise
        ]);

        const duration = Date.now() - checkStart;
        
        service.lastCheck = Date.now();
        service.status = 'healthy';

        results.services[name] = {
          status: 'healthy',
          duration,
          details: healthResult,
          critical: service.critical
        };

        results.summary.healthy++;

      } catch (error) {
        service.status = 'unhealthy';
        
        results.services[name] = {
          status: 'unhealthy',
          error: error.message,
          critical: service.critical
        };

        results.summary.unhealthy++;
        
        if (service.critical) {
          results.summary.critical_failed++;
          results.status = 'unhealthy';
        }
      }
    });

    await Promise.all(checks);
    
    results.summary.total = this.services.size;
    results.duration = Date.now() - startTime;

    return results;
  }

  /**
   * Basic liveness check
   */
  async liveness() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime
    };
  }

  /**
   * Readiness check for critical services
   */
  async readiness() {
    const critical = Array.from(this.services.values()).filter(s => s.critical);
    
    for (const service of critical) {
      try {
        await service.healthCheck();
      } catch (error) {
        return {
          status: 'not_ready',
          error: `Critical service ${service.name} failed: ${error.message}`,
          timestamp: new Date().toISOString()
        };
      }
    }

    return {
      status: 'ready',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get service status
   */
  getServiceStatus(serviceName) {
    const service = this.services.get(serviceName);
    
    if (!service) {
      return { error: 'Service not found' };
    }

    return {
      name: serviceName,
      status: service.status,
      lastCheck: service.lastCheck,
      critical: service.critical
    };
  }
}

// Create singleton instance
const healthCheck = new HealthCheckService();

// Register core services
healthCheck.registerService('database', async () => {
  // Check Supabase connection
  try {
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/rest/v1/`, {
      headers: {
        'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY
      }
    });
    
    if (response.ok) {
      return { database: 'connected' };
    } else {
      throw new Error(`Database connection failed: ${response.status}`);
    }
  } catch (error) {
    throw new Error(`Database health check failed: ${error.message}`);
  }
}, { critical: true });

healthCheck.registerService('ai_providers', async () => {
  const providers = [];
  
  // Check OpenAI
  if (import.meta.env.VITE_OPENAI_API_KEY) {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
        }
      });
      providers.push({ provider: 'openai', status: response.ok ? 'healthy' : 'failed' });
    } catch (error) {
      providers.push({ provider: 'openai', status: 'failed', error: error.message });
    }
  }

  // Check Anthropic
  if (import.meta.env.VITE_ANTHROPIC_API_KEY) {
    try {
      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'x-api-key': import.meta.env.VITE_ANTHROPIC_API_KEY,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'test' }]
        })
      });
      providers.push({ provider: 'anthropic', status: response.ok ? 'healthy' : 'failed' });
    } catch (error) {
      providers.push({ provider: 'anthropic', status: 'failed', error: error.message });
    }
  }

  return { providers };
}, { critical: false });

healthCheck.registerService('speech_engine', async () => {
  // Basic check for speech engine readiness
  const hasWebSocket = typeof WebSocket !== 'undefined';
  const hasMediaRecorder = typeof MediaRecorder !== 'undefined';
  
  return {
    websocket_support: hasWebSocket,
    media_recorder_support: hasMediaRecorder,
    ready: hasWebSocket && hasMediaRecorder
  };
}, { critical: true });

healthCheck.registerService('storage', async () => {
  // Check IndexedDB availability
  const hasIndexedDB = typeof indexedDB !== 'undefined';
  
  if (hasIndexedDB) {
    try {
      // Test IndexedDB connection
      const request = indexedDB.open('health-check-test', 1);
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          request.result.close();
          resolve({ indexeddb: 'available' });
        };
        request.onerror = () => reject(new Error('IndexedDB test failed'));
      });
    } catch (error) {
      throw new Error(`Storage check failed: ${error.message}`);
    }
  } else {
    throw new Error('IndexedDB not available');
  }
}, { critical: false });

// Health check endpoints for Express/API routes
export const healthEndpoints = {
  '/health': async () => await healthCheck.performHealthCheck(),
  '/health/liveness': async () => await healthCheck.liveness(),
  '/health/readiness': async () => await healthCheck.readiness()
};

export default healthCheck;
