/**
 * Speech-to-Text Service using OpenAI Whisper API
 */
class SpeechToTextService {
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    this.baseUrl = 'https://api.openai.com/v1/audio/transcriptions';
    this.model = 'whisper-1';
  }

  /**
   * Transcribe audio blob to text using Whisper API
   */
  async transcribeAudio(audioBlob, options = {}) {
    try {
      console.log('🎤 Starting Whisper transcription...', { 
        size: audioBlob.size, 
        type: audioBlob.type 
      });

      const formData = new FormData();
      formData.append('file', audioBlob, 'audio.webm');
      formData.append('model', this.model);
      formData.append('language', options.language || 'en');
      formData.append('response_format', 'json');
      formData.append('temperature', '0.2');

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bear<PERSON> ${this.apiKey}`,
        },
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Whisper API error:', response.status, errorText);
        throw new Error(`Whisper API error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Whisper transcription successful:', result.text);

      return {
        text: result.text,
        confidence: 0.95, // Whisper doesn't provide confidence scores
        duration: audioBlob.size / 16000, // Rough estimate
        language: options.language || 'en'
      };

    } catch (error) {
      console.error('❌ Speech-to-text error:', error);
      
      // Fallback to mock transcription for development
      if (error.message.includes('API key') || error.message.includes('401')) {
        console.warn('🔄 Falling back to mock transcription - API key not configured');
        return this.getMockTranscription();
      }
      
      throw error;
    }
  }

  /**
   * Mock transcription for development/testing
   */
  getMockTranscription() {
    const mockTranscriptions = [
      "I've been experiencing some chest discomfort and shortness of breath.",
      "Can you help me understand what might be causing these symptoms?",
      "The pain is usually sharp and happens when I exercise.",
      "I'm also feeling tired more often than usual.",
      "Should I be concerned about these symptoms?",
      "I have a headache that's been persistent for the past few days.",
      "I'm experiencing some digestive issues after meals.",
      "My sleep has been disrupted and I feel restless at night.",
      "I've noticed some changes in my appetite recently.",
      "Can you recommend any immediate steps I should take?"
    ];
    
    const randomText = mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];
    
    return {
      text: randomText,
      confidence: 0.9,
      duration: 2.5,
      language: 'en',
      isMock: true
    };
  }

  /**
   * Check if service is configured properly
   */
  isConfigured() {
    return Boolean(this.apiKey);
  }

  /**
   * Health check for the service
   */
  async healthCheck() {
    try {
      if (!this.apiKey) {
        return { status: 'error', message: 'API key not configured' };
      }
      
      // Could ping a lightweight endpoint here
      return { status: 'healthy', message: 'Service configured and ready' };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }
}

export default new SpeechToTextService();
